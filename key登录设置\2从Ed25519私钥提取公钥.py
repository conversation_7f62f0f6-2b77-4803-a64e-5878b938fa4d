from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.serialization import load_pem_private_key

def extract_public_key():
    # 读取私钥文件
    try:
        with open('private_key.pem', 'rb') as f:
            private_key = load_pem_private_key(f.read(), password=None)
            
        # 获取公钥
        public_key = private_key.public_key()
        
        # 将公钥序列化为PEM格式
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        # 保存公钥
        with open('public_key.pem', 'wb') as f:
            f.write(public_pem)
            
        print("已从私钥提取公钥并保存到 public_key.pem")
        print("\n公钥内容（可复制到Binance）：")
        print(public_pem.decode('utf-8'))
        
    except FileNotFoundError:
        print("错误：找不到 private_key.pem 文件")
    except Exception as e:
        print(f"错误：{str(e)}")

if __name__ == "__main__":
    extract_public_key()