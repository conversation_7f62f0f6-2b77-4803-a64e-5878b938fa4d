#!/usr/bin/env python3
"""
生成标准OpenSSH格式的Ed25519密钥对
适用于SSH登录、Termius等SSH客户端
"""

from cryptography.hazmat.primitives.asymmetric import ed25519
from cryptography.hazmat.primitives import serialization
import os
import stat
from datetime import datetime

def generate_openssh_ed25519_keypair(key_name="id_ed25519", comment=None):
    """
    生成OpenSSH格式的Ed25519密钥对
    
    Args:
        key_name (str): 密钥文件名前缀，默认为 "id_ed25519"
        comment (str): 公钥注释，默认为生成时间戳
    
    Returns:
        tuple: (私钥文件路径, 公钥文件路径)
    """
    
    # 生成Ed25519密钥对
    print("正在生成Ed25519密钥对...")
    private_key = ed25519.Ed25519PrivateKey.generate()
    public_key = private_key.public_key()
    
    # 设置默认注释
    if comment is None:
        comment = f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    
    # 文件路径
    private_key_path = f"{key_name}"
    public_key_path = f"{key_name}.pub"
    
    # 生成OpenSSH格式的私钥
    private_key_bytes = private_key.private_bytes(
        encoding=serialization.Encoding.OpenSSH,
        format=serialization.PrivateFormat.OpenSSH,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    # 生成OpenSSH格式的公钥
    public_key_bytes = public_key.public_bytes(
        encoding=serialization.Encoding.OpenSSH,
        format=serialization.PublicFormat.OpenSSH
    )
    
    # 保存私钥文件
    with open(private_key_path, "wb") as f:
        f.write(private_key_bytes)
    
    # 设置私钥文件权限为600 (仅所有者可读写)
    os.chmod(private_key_path, stat.S_IRUSR | stat.S_IWUSR)
    
    # 保存公钥文件 (添加注释)
    public_key_with_comment = public_key_bytes.decode('utf-8').strip() + f" {comment}\n"
    with open(public_key_path, "w", encoding='utf-8') as f:
        f.write(public_key_with_comment)
    
    # 设置公钥文件权限为644 (所有者可读写，其他人只读)
    os.chmod(public_key_path, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
    
    return private_key_path, public_key_path

def main():
    """主函数"""
    print("=== OpenSSH Ed25519 密钥对生成器 ===\n")
    
    # 获取用户输入
    key_name = input("请输入密钥文件名 (默认: id_ed25519): ").strip()
    if not key_name:
        key_name = "id_ed25519"
    
    comment = input("请输入公钥注释 (默认: 自动生成时间戳): ").strip()
    if not comment:
        comment = None
    
    try:
        # 检查文件是否已存在
        if os.path.exists(key_name) or os.path.exists(f"{key_name}.pub"):
            overwrite = input(f"文件 {key_name} 或 {key_name}.pub 已存在，是否覆盖? (y/N): ").strip().lower()
            if overwrite != 'y':
                print("操作已取消。")
                return
        
        # 生成密钥对
        private_path, public_path = generate_openssh_ed25519_keypair(key_name, comment)
        
        print(f"\n✅ 密钥对生成成功!")
        print(f"📁 私钥文件: {private_path}")
        print(f"📁 公钥文件: {public_path}")
        print(f"🔒 私钥权限: 600 (仅所有者可读写)")
        print(f"🔓 公钥权限: 644 (所有者可读写，其他人只读)")
        
        # 显示公钥内容
        print(f"\n📋 公钥内容 (用于添加到服务器的 ~/.ssh/authorized_keys):")
        with open(public_path, 'r', encoding='utf-8') as f:
            print(f.read().strip())
        
        print(f"\n📖 使用说明:")
        print(f"1. 将公钥内容添加到目标服务器的 ~/.ssh/authorized_keys 文件")
        print(f"2. 在SSH客户端(如Termius)中导入私钥文件: {private_path}")
        print(f"3. 使用SSH连接: ssh -i {private_path} user@server")
        
    except Exception as e:
        print(f"❌ 生成密钥对时出错: {e}")

if __name__ == "__main__":
    main()
