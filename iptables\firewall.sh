#!/bin/bash
# Debian VPS 防火墙一键配置脚本 (系统准备 + 防火墙配置)

# 检查root权限
if [[ $EUID -ne 0 ]]; then
   echo "❌ 请使用root用户运行"
   exit 1
fi

# 检查.env文件
if [[ ! -f ".env" ]]; then
    echo "❌ 找不到.env文件"
    exit 1
fi

echo "🚀 开始Debian系统防火墙配置..."

# 1. 系统环境准备
echo "📦 准备系统环境..."
apt update -qq
apt install -y iptables iptables-persistent netfilter-persistent >/dev/null 2>&1

# 禁用冲突的防火墙
if command -v ufw >/dev/null 2>&1; then
    ufw --force disable >/dev/null 2>&1
    systemctl disable ufw >/dev/null 2>&1
    echo "✅ 已禁用ufw"
fi

systemctl enable netfilter-persistent >/dev/null 2>&1
echo "✅ 系统环境准备完成"

# 2. 加载防火墙配置
source .env
echo "🔥 开始配置防火墙..."

# 3. 备份当前规则
iptables-save > "/root/iptables_backup_$(date +%Y%m%d_%H%M%S).txt"

# 4. 清空规则 (保留Docker链)
echo "🔄 清空防火墙规则 (保留Docker链)..."
# 只清空INPUT链，保留Docker相关链
iptables -F INPUT
iptables -F OUTPUT
# 不清空FORWARD链和Docker链

# 5. 设置默认策略 (Docker兼容)
iptables -P INPUT DROP
iptables -P OUTPUT ACCEPT
# 不修改FORWARD策略，让Docker管理

# 6. 允许本地回环
iptables -A INPUT -i lo -j ACCEPT

# 7. 允许已建立的连接
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# 8. 添加允许的IP (SSH访问)
echo "🛡️ 配置SSH访问控制..."
IFS=',' read -ra IPS <<< "$ALLOWED_IPS"
for ip in "${IPS[@]}"; do
    ip=$(echo "$ip" | xargs)  # 去除空格
    if [[ -n "$ip" ]]; then
        iptables -A INPUT -s "$ip" -p tcp --dport "$SSH_PORT" -j ACCEPT
        echo "✅ 允许IP $ip 访问SSH端口 $SSH_PORT"
    fi
done

# 9. 开放公共端口
echo "🌐 开放公共端口..."
IFS=',' read -ra PORTS <<< "$PUBLIC_PORTS"
for port in "${PORTS[@]}"; do
    port=$(echo "$port" | xargs)  # 去除空格
    if [[ -n "$port" && "$port" =~ ^[0-9]+$ ]]; then
        iptables -A INPUT -p tcp --dport "$port" -j ACCEPT
        echo "✅ 开放端口: $port"
    fi
done

# 10. 允许Docker容器间通信
echo "🐳 配置Docker网络支持..."
iptables -A INPUT -i docker0 -j ACCEPT
iptables -A INPUT -i br-+ -j ACCEPT

# 11. 保存规则
mkdir -p /etc/iptables
iptables-save > /etc/iptables/rules.v4

# 11. 创建开机自启服务
cat > /etc/systemd/system/iptables-restore.service << 'EOF'
[Unit]
Description=Restore iptables rules
After=network.target

[Service]
Type=oneshot
ExecStart=/sbin/iptables-restore /etc/iptables/rules.v4
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable iptables-restore.service >/dev/null 2>&1
systemctl start iptables-restore.service >/dev/null 2>&1

# 12. 重启Docker服务以重建网络链
echo "🐳 重启Docker服务..."
systemctl restart docker >/dev/null 2>&1
sleep 3

echo ""
echo "🎉 Debian VPS 防火墙配置完成！"
echo "🛡️ SSH端口 $SSH_PORT 仅允许IP: $ALLOWED_IPS"
echo "🌐 公开访问端口: $PUBLIC_PORTS"
echo "🐳 Docker网络支持已启用"
echo "✅ 系统环境已准备，可以部署其他服务"
echo ""
echo "📋 管理命令:"
echo "   查看规则: iptables -L -n"
echo "   重启服务: systemctl restart iptables-restore.service"
echo "   重启Docker: systemctl restart docker"
