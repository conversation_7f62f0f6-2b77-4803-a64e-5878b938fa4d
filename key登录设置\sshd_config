# SSH服务器配置文件 - 优化版
# 适用于：22端口 + 密钥登录 + root用户 + 禁用密码登录

# 基本设置
Port 22
Protocol 2
AddressFamily any
ListenAddress 0.0.0.0
ListenAddress ::

# 主机密钥
HostKey /etc/ssh/ssh_host_rsa_key
HostKey /etc/ssh/ssh_host_ecdsa_key
HostKey /etc/ssh/ssh_host_ed25519_key

# 密钥交换、加密和MAC算法 (现代安全标准)
KexAlgorithms curve25519-sha256,<EMAIL>,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp521,diffie-hellman-group16-sha512,diffie-hellman-group18-sha512
Ciphers <EMAIL>,<EMAIL>,<EMAIL>,aes256-ctr,aes192-ctr,aes128-ctr
MACs <EMAIL>,<EMAIL>,hmac-sha2-256,hmac-sha2-512

# 认证设置
LoginGraceTime 60
PermitRootLogin yes
StrictModes yes
MaxAuthTries 3
MaxSessions 10

# 公钥认证 (启用)
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys

# 密码认证 (禁用)
PasswordAuthentication no
PermitEmptyPasswords no
ChallengeResponseAuthentication no

# Kerberos认证 (禁用)
KerberosAuthentication no
KerberosOrLocalPasswd no
KerberosTicketCleanup yes

# GSSAPI认证 (禁用)
GSSAPIAuthentication no
GSSAPICleanupCredentials yes

# 其他认证方法 (禁用)
UsePAM yes
AuthenticationMethods publickey

# 网络设置
X11Forwarding no
X11DisplayOffset 10
PrintMotd no
PrintLastLog yes
TCPKeepAlive yes
Compression delayed
ClientAliveInterval 300
ClientAliveCountMax 2

# 环境设置
AcceptEnv LANG LC_*
Subsystem sftp /usr/lib/openssh/sftp-server

# 安全设置
PermitUserEnvironment no
AllowAgentForwarding yes
AllowTcpForwarding yes
GatewayPorts no
PermitTunnel no

# 日志设置
SyslogFacility AUTH
LogLevel INFO

# 性能优化
UseDNS no
PermitTTY yes

# Banner设置 (可选)
# Banner /etc/issue.net

# 用户限制 (可根据需要调整)
# AllowUsers root
# DenyUsers guest
# AllowGroups ssh-users
# DenyGroups no-ssh
