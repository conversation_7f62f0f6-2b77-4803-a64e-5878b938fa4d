# SSH密钥登录配置指南

## 📋 概述
将Debian服务器从密码登录改为SSH密钥登录，提高安全性。

## 🎯 目标
- **当前状态**: root用户 + 密码登录
- **目标状态**: root用户 + SSH密钥登录
- **禁用密码登录**: 提高安全性

---

## 🔧 操作步骤

### 步骤1: 准备密钥文件
确保你已经生成了密钥对：
- **私钥**: `claw_ed25519` (保存在本地)
- **公钥**: `claw_ed25519.pub` (需要上传到服务器)

### 步骤2: 连接到服务器
```bash
# 使用密码登录到服务器
ssh root@*************
```

### 步骤3: 创建SSH目录和文件
```bash
# 创建.ssh目录 (如果不存在)
mkdir -p ~/.ssh

# 设置.ssh目录权限
chmod 700 ~/.ssh

# 创建authorized_keys文件 (如果不存在)
touch ~/.ssh/authorized_keys

# 设置authorized_keys文件权限
chmod 600 ~/.ssh/authorized_keys
```

### 步骤4: 添加公钥到服务器

#### 方法A: 直接编辑文件
```bash
# 编辑authorized_keys文件
nano ~/.ssh/authorized_keys
```
然后将你的公钥内容粘贴进去，格式如下：
```
ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJoovR2T9195EaVVANvPr68cfljDeia4VjsEDk4g6cO8 claw server - Generated on 2025-08-05 20:31:16
```

#### 方法B: 使用echo命令
```bash
# 直接添加公钥 (替换为你的实际公钥内容)
echo "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJoovR2T9195EaVVANvPr68cfljDeia4VjsEDk4g6cO8 claw server - Generated on 2025-08-05 20:31:16" >> ~/.ssh/authorized_keys
```

### 步骤5: 验证密钥登录
**⚠️ 重要：不要关闭当前SSH连接！**

打开新的终端窗口测试密钥登录：
```bash
# 测试密钥登录 (替换为你的实际文件路径和服务器IP)
ssh -i /path/to/claw_ed25519 root@*************
```

如果成功登录，说明密钥配置正确。

### 步骤6: 配置SSH服务器 (禁用密码登录)
```bash
# 备份SSH配置文件
cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

# 编辑SSH配置
nano /etc/ssh/sshd_config
```

**nano编辑器操作：清空文件内容，然后粘贴新配置**
```bash
# 清空文件内容的快捷键
Alt+A      # 开始标记选择
Ctrl+End   # 移动到文件末尾 (或 Alt+/)
Ctrl+K     # 删除标记的内容

# 然后粘贴以下完整配置
```

```bash
# SSH服务器配置文件 - 精简版
# 仅保留核心必要配置

# 基本设置
Port 22
PermitRootLogin yes

# 密钥认证 (启用)
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys

# 密码认证 (禁用)
PasswordAuthentication no
PermitEmptyPasswords no

# 安全设置
MaxAuthTries 3
UseDNS no

# 必要的系统设置
UsePAM yes
Subsystem sftp /usr/lib/openssh/sftp-server
```

### 步骤7: 重启SSH服务
```bash
# 重启SSH服务
systemctl restart sshd

# 检查SSH服务状态
systemctl status sshd
```

### 步骤8: 最终测试
1. **保持当前SSH连接不断开**
2. **打开新终端测试密钥登录**：
   ```bash
   ssh -i /path/to/claw_ed25519 root@your_server_ip
   ```
3. **测试密码登录被禁用**：
   ```bash
   ssh root@your_server_ip
   # 应该提示 "Permission denied (publickey)"
   ```

---

## 🔍 故障排除

### nano编辑器常用操作
```bash
# 文件操作
Ctrl+X     # 保存并退出
Ctrl+O     # 保存文件 (不退出)
Ctrl+R     # 读取/插入其他文件

# 编辑操作
Alt+A      # 开始标记选择
Ctrl+End   # 移动到文件末尾
Ctrl+Home  # 移动到文件开头
Ctrl+K     # 删除当前行或选中内容
Ctrl+U     # 撤销删除
Ctrl+W     # 搜索文本
Ctrl+\     # 查找并替换

# 导航操作
Ctrl+Y     # 上一页
Ctrl+V     # 下一页
Ctrl+G     # 跳转到指定行号
```

### 问题1: 密钥登录失败
```bash
# 检查SSH日志
tail -f /var/log/auth.log

# 检查文件权限
ls -la ~/.ssh/
ls -la ~/.ssh/authorized_keys
```

### 问题2: 权限错误
```bash
# 重新设置正确权限
chmod 700 ~/.ssh
chmod 600 ~/.ssh/authorized_keys
chown root:root ~/.ssh
chown root:root ~/.ssh/authorized_keys
```

### 问题3: 配置文件语法错误
```bash
# 检查SSH配置语法
sshd -t

# 如果有错误，恢复备份
cp /etc/ssh/sshd_config.backup /etc/ssh/sshd_config
systemctl restart sshd
```

---

## 📱 客户端配置

### Termius配置
1. 打开Termius
2. 添加新主机
3. 导入私钥文件：`claw_ed25519`
4. 设置用户名：`root`
5. 设置服务器IP
6. 保存并连接

### SSH命令行配置
创建SSH配置文件：
```bash
# 编辑本地SSH配置
nano ~/.ssh/config
```

添加配置：
```
Host claw
    HostName your_server_ip
    User root
    IdentityFile /path/to/claw_ed25519
    IdentitiesOnly yes
```

然后可以简单使用：
```bash
ssh claw
```

---

## ✅ 安全检查清单

- [ ] 密钥登录测试成功
- [ ] 密码登录已禁用
- [ ] SSH服务正常运行
- [ ] 文件权限设置正确
- [ ] 备份了SSH配置文件
- [ ] 客户端可以正常连接

---

## ⚠️ 重要提醒

1. **备份重要**：操作前备份SSH配置文件
2. **保持连接**：配置过程中保持至少一个SSH连接
3. **测试充分**：确保密钥登录成功后再禁用密码登录
4. **私钥安全**：妥善保管私钥文件，不要泄露
5. **多重验证**：建议在多个客户端测试连接
